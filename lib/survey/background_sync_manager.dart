import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/survey/connectivity_sync_service.dart';
import 'package:schnell_pole_installation/utils/utility.dart';

/// Background sync manager that coordinates survey data synchronization
class BackgroundSyncManager {
  static BackgroundSyncManager? _instance;
  static BackgroundSyncManager get instance =>
      _instance ??= BackgroundSyncManager._();

  BackgroundSyncManager._();

  final ConnectivitySyncService _syncService = ConnectivitySyncService.instance;

  // Sync state
  bool _isInitialized = false;
  bool _isSyncing = false;

  // Track initialization to prevent multiple attempts
  Future<void>? _initializationFuture;
  bool _statusListenerSetup = false;

  // Progress and status streams
  Stream<SyncProgress> get progressStream => _syncService.progressStream;
  Stream<SyncStatus> get statusStream => _syncService.statusStream;

  // Sync statistics
  int _lastSyncCount = 0;
  DateTime? _lastSyncTime;
  String? _lastSyncError;

  /// Initialize the background sync manager
  Future<void> initialize() async {
    // If already initialized, return immediately
    if (_isInitialized) return;

    // If initialization is in progress, wait for it to complete
    if (_initializationFuture != null) {
      return _initializationFuture!;
    }

    // Start initialization
    _initializationFuture = _performInitialization();

    try {
      await _initializationFuture!;
    } finally {
      _initializationFuture = null;
    }
  }

  /// Perform the actual initialization
  Future<void> _performInitialization() async {
    try {
      log('Initializing BackgroundSyncManager...');

      // Initialize the background sync service
      await _syncService.initialize();

      // Listen to sync status changes (only once)
      if (!_statusListenerSetup) {
        _syncService.statusStream.listen(_handleSyncStatusChange);
        _statusListenerSetup = true;
      }

      _isInitialized = true;
      log('BackgroundSyncManager initialized successfully');
    } catch (e) {
      log('Failed to initialize BackgroundSyncManager: $e');
      _lastSyncError = e.toString();
      rethrow;
    }
  }

  /// Stop connectivity monitoring (if needed)
  void stopConnectivityMonitoring() {
    _syncService.stopMonitoring();
    log('Connectivity monitoring stopped');
  }

  /// Start background sync for both survey data and images
  Future<void> startBackgroundSync() async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_isSyncing) {
      log('Background sync already in progress, skipping...');
      return;
    }

    try {
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('No internet connection available for background sync');
        return;
      }

      _isSyncing = true;
      _lastSyncError = null;

      log('Starting background sync...');

      // Trigger immediate sync (connectivity-based, no periodic sync)
      await _syncService.performManualSync();
    } catch (e) {
      log('Error starting background sync: $e');
      _lastSyncError = e.toString();
      _isSyncing = false;
    }
  }

  /// Start background sync for survey data only
  Future<void> startSurveySync() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('No internet connection available for survey sync');
        return;
      }

      log('Starting immediate survey sync...');
      await _syncService.performManualSync();
    } catch (e) {
      log('Error starting survey sync: $e');
      _lastSyncError = e.toString();
    }
  }

  /// Start background sync for images only
  Future<void> startImageSync() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      bool isOnline = await Utility.isConnected();
      if (!isOnline) {
        log('No internet connection available for image sync');
        return;
      }

      log('Starting immediate image sync...');
      await _syncService.performManualSync();
    } catch (e) {
      log('Error starting image sync: $e');
      _lastSyncError = e.toString();
    }
  }

  /// Handle sync status changes
  void _handleSyncStatusChange(SyncStatus status) {
    switch (status) {
      case SyncStatus.syncing:
        _isSyncing = true;
        break;

      case SyncStatus.completed:
        _isSyncing = false;
        _lastSyncTime = DateTime.now();
        _lastSyncError = null;
        log('Background sync completed successfully');
        break;

      case SyncStatus.error:
        _isSyncing = false;
        log('Background sync failed');
        break;

      case SyncStatus.idle:
        _isSyncing = false;
        break;
    }
  }

  /// Get sync status information
  SyncStatusInfo get syncStatusInfo {
    return SyncStatusInfo(
      isSyncing: _isSyncing,
      isInitialized: _isInitialized,
      lastSyncTime: _lastSyncTime,
      lastSyncError: _lastSyncError,
      lastSyncCount: _lastSyncCount,
    );
  }

  /// Dispose the manager
  void dispose() {
    stopConnectivityMonitoring();
    _syncService.dispose();
    _isInitialized = false;
    _isSyncing = false;
    _statusListenerSetup = false;
    _initializationFuture = null;
  }

  /// Get current sync status
  bool get isSyncing => _isSyncing;
  bool get isInitialized => _isInitialized;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get lastSyncError => _lastSyncError;
}

/// Sync status information class
class SyncStatusInfo {
  final bool isSyncing;
  final bool isInitialized;
  final DateTime? lastSyncTime;
  final String? lastSyncError;
  final int lastSyncCount;

  SyncStatusInfo({
    required this.isSyncing,
    required this.isInitialized,
    this.lastSyncTime,
    this.lastSyncError,
    required this.lastSyncCount,
  });

  /// Get human-readable status text
  String get statusText {
    if (!isInitialized) return 'Not initialized';
    if (isSyncing) return 'Syncing...';
    if (lastSyncError != null) return 'Sync failed';
    if (lastSyncTime != null) {
      final duration = DateTime.now().difference(lastSyncTime!);
      if (duration.inMinutes < 1) {
        return 'Synced just now';
      } else if (duration.inHours < 1) {
        return 'Synced ${duration.inMinutes}m ago';
      } else {
        return 'Synced ${duration.inHours}h ago';
      }
    }
    return 'Ready to sync';
  }

  /// Get status color for UI
  Color get statusColor {
    if (!isInitialized) return Colors.grey;
    if (isSyncing) return Colors.blue;
    if (lastSyncError != null) return Colors.red;
    if (lastSyncTime != null) return Colors.green;
    return Colors.orange;
  }

  /// Get status icon for UI
  IconData get statusIcon {
    if (!isInitialized) return Icons.cloud_off;
    if (isSyncing) return Icons.cloud_sync;
    if (lastSyncError != null) return Icons.cloud_off;
    if (lastSyncTime != null) return Icons.cloud_done;
    return Icons.cloud_queue;
  }
}
